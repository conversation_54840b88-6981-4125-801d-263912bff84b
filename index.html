<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Kumbh Mela Navigation - Triveni Ghat, Ujjain</title>

  <!-- Leaflet CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine/dist/leaflet-routing-machine.css" />
  <link rel="stylesheet" href="https://unpkg.com/leaflet.heat/dist/leaflet-heat.css" />

  <!-- Mobile viewport optimization -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="theme-color" content="#FF6B35">

  <!-- Preload critical resources -->
  <link rel="preload" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" as="style">
  <link rel="preload" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" as="script">

  <!-- PWA Manifest -->
  <link rel="manifest" href="manifest.json">

  <!-- Favicon -->
  <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🕉️</text></svg>">
  
  <style>
    * {
      box-sizing: border-box;
      -webkit-tap-highlight-color: transparent;
    }

    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      overflow-x: hidden;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* App Container */
    .app-container {
      position: relative;
      width: 100%;
      height: 100vh;
      overflow: hidden;
    }

    /* Screen Management */
    .screen {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: white;
      transform: translateX(100%);
      transition: transform 0.3s ease-in-out;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .screen.active {
      transform: translateX(0);
    }

    /* Dashboard Styles */
    .dashboard-header {
      background: linear-gradient(135deg, #FF6B35, #F7931E);
      color: white;
      padding: 20px;
      text-align: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .dashboard-header h1 {
      margin: 0;
      font-size: 24px;
      font-weight: bold;
    }

    .dashboard-header p {
      margin: 5px 0 15px 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .location-status {
      background: rgba(255,255,255,0.2);
      padding: 8px 15px;
      border-radius: 20px;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
    }

    .location-icon {
      animation: pulse 2s infinite;
    }

    /* Dashboard Grid */
    .dashboard-grid {
      padding: 20px;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
      max-width: 600px;
      margin: 0 auto;
    }

    .dashboard-card {
      background: white;
      border-radius: 15px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      cursor: pointer;
      transition: all 0.3s ease;
      min-height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .dashboard-card:active {
      transform: scale(0.95);
    }

    .admin-card {
      border: 2px solid #dc3545;
      background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
    }

    .admin-card .card-icon {
      color: #dc3545;
    }

    .admin-card h3 {
      color: #dc3545;
    }

    .dashboard-card .card-icon {
      font-size: 32px;
      margin-bottom: 10px;
    }

    .dashboard-card h3 {
      margin: 0 0 8px 0;
      font-size: 16px;
      color: #333;
    }

    .dashboard-card p {
      margin: 0;
      font-size: 12px;
      color: #666;
      line-height: 1.4;
    }
    /* Screen Headers */
    .screen-header {
      background: linear-gradient(135deg, #FF6B35, #F7931E);
      color: white;
      padding: 15px 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .back-btn, .refresh-btn, .heat-toggle, .locations-toggle {
      background: rgba(255,255,255,0.2);
      border: none;
      color: white;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;
    }

    .back-btn:active, .refresh-btn:active, .heat-toggle:active, .locations-toggle:active {
      background: rgba(255,255,255,0.3);
    }

    .header-controls {
      display: flex;
      gap: 8px;
    }

    .screen-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 15px;
    }

    .screen-header h2 {
      flex: 1;
      text-align: center;
      margin: 0;
    }

    .screen-header h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    /* Map Styles */
    #map {
      width: 100%;
      height: calc(100% - 60px - 80px); /* Subtract header and controls */
      position: relative;
    }

    .map-controls {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: white;
      padding: 15px;
      box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
      z-index: 1000;
    }

    .control-group {
      margin-bottom: 10px;
    }

    .control-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 600;
      color: #333;
      font-size: 14px;
    }

    .control-group select {
      width: 100%;
      padding: 12px;
      border: 2px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      background: white;
    }

    .control-buttons {
      display: flex;
      gap: 10px;
    }

    .primary-btn, .secondary-btn {
      flex: 1;
      padding: 12px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .primary-btn {
      background: linear-gradient(135deg, #FF6B35, #F7931E);
      color: white;
    }

    .secondary-btn {
      background: #f8f9fa;
      color: #333;
      border: 2px solid #ddd;
    }

    .primary-btn:active, .secondary-btn:active {
      transform: scale(0.95);
    }
    /* Category Tabs */
    .category-tabs {
      display: flex;
      overflow-x: auto;
      padding: 15px 20px 0;
      gap: 10px;
      -webkit-overflow-scrolling: touch;
    }

    .tab-btn {
      background: #f8f9fa;
      border: 2px solid #ddd;
      color: #666;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      white-space: nowrap;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .tab-btn.active {
      background: linear-gradient(135deg, #FF6B35, #F7931E);
      color: white;
      border-color: #FF6B35;
    }

    /* Places List */
    .places-list {
      padding: 20px;
    }

    .place-item {
      background: white;
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 15px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .place-item:active {
      transform: scale(0.98);
    }

    .place-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;
    }

    .place-icon {
      font-size: 24px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;
      border-radius: 10px;
    }

    .place-info h4 {
      margin: 0;
      font-size: 16px;
      color: #333;
    }

    .place-info p {
      margin: 2px 0 0 0;
      font-size: 12px;
      color: #666;
    }

    .place-distance {
      margin-left: auto;
      text-align: right;
      font-size: 12px;
      color: #FF6B35;
      font-weight: 600;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;
    }

    .navigate-btn {
      background: #FF6B35;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 10px;
    }

    .place-details {
      display: flex;
      gap: 10px;
      margin-top: 4px;
      font-size: 11px;
    }

    .place-rating {
      color: #ffc107;
    }

    .place-hours {
      color: #666;
    }

    .place-facilities {
      font-size: 10px;
      color: #888;
      margin-top: 2px;
      font-style: italic;
    }

    /* Form Styles */
    .form-container {
      padding: 20px;
      max-width: 600px;
      margin: 0 auto;
    }

    .mobile-form {
      background: white;
      border-radius: 15px;
      padding: 20px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #333;
      font-size: 14px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 12px;
      border: 2px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      transition: border-color 0.3s ease;
      background: white;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: #FF6B35;
      box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
    }

    .submit-btn {
      width: 100%;
      padding: 15px;
      background: linear-gradient(135deg, #FF6B35, #F7931E);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .submit-btn:active {
      transform: scale(0.98);
    }

    /* Loading and Toast Styles */
    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.7);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      color: white;
      text-align: center;
    }

    .loading-overlay.show {
      display: flex;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255,255,255,0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 15px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .toast-container {
      position: fixed;
      top: 20px;
      left: 20px;
      right: 20px;
      z-index: 10000;
      pointer-events: none;
    }

    .toast {
      background: #333;
      color: white;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 10px;
      transform: translateY(-100px);
      opacity: 0;
      transition: all 0.3s ease;
      pointer-events: auto;
    }

    .toast.show {
      transform: translateY(0);
      opacity: 1;
    }

    .toast.success {
      background: #28a745;
    }

    .toast.error {
      background: #dc3545;
    }

    .toast.warning {
      background: #ffc107;
      color: #333;
    }

    /* Old Dashboard (hidden by default) */
    .dashboard {
      display: none;
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1000;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      max-width: 350px;
      transition: all 0.3s ease;
    }

    .dashboard-header {
      background: linear-gradient(135deg, #FF6B35, #F7931E);
      color: white;
      padding: 15px;
      border-radius: 12px 12px 0 0;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .dashboard-content {
      padding: 0;
      max-height: 0;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .dashboard.expanded .dashboard-content {
      max-height: 600px;
      padding: 20px;
    }

    .form-tabs {
      display: flex;
      margin-bottom: 20px;
      border-bottom: 2px solid #f0f0f0;
    }

    .tab-button {
      flex: 1;
      padding: 10px 5px;
      border: none;
      background: none;
      cursor: pointer;
      font-size: 12px;
      font-weight: bold;
      color: #666;
      transition: all 0.3s ease;
      border-bottom: 3px solid transparent;
    }

    .tab-button.active {
      color: #FF6B35;
      border-bottom-color: #FF6B35;
    }

    .form-content {
      display: none;
    }

    .form-content.active {
      display: block;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #333;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 8px;
      border: 2px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      transition: border-color 0.3s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: #FF6B35;
    }

    .submit-btn {
      width: 100%;
      padding: 12px;
      background: linear-gradient(135deg, #FF6B35, #F7931E);
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: transform 0.2s ease;
    }

    .submit-btn:hover {
      transform: translateY(-2px);
    }

    .route-info {
      position: absolute;
      bottom: 10px;
      left: 10px;
      right: 10px;
      z-index: 1000;
      background: white;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      max-height: 60vh;
      overflow-y: auto;
      display: none;
    }

    .route-info.visible {
      display: block;
    }

    .route-stats {
      display: flex;
      justify-content: space-between;
      margin: 15px 0;
      gap: 10px;
    }

    .stat-item {
      flex: 1;
      text-align: center;
      padding: 10px;
      background: #f8f9fa;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 5px;
    }

    .stat-icon {
      font-size: 20px;
    }

    .stat-info strong {
      display: block;
      font-size: 16px;
      color: #333;
    }

    .stat-info small {
      font-size: 11px;
      color: #666;
    }

    .route-conditions {
      margin: 15px 0;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .condition-item {
      margin: 5px 0;
      font-size: 14px;
    }

    .route-instructions {
      margin: 15px 0;
    }

    .instructions-list {
      max-height: 200px;
      overflow-y: auto;
      margin-top: 10px;
    }

    .instruction-item {
      display: flex;
      align-items: flex-start;
      gap: 10px;
      padding: 8px 0;
      border-bottom: 1px solid #eee;
    }

    .instruction-item:last-child {
      border-bottom: none;
    }

    .instruction-icon {
      font-size: 16px;
      width: 24px;
      text-align: center;
      margin-top: 2px;
    }

    .instruction-text strong {
      display: block;
      font-size: 14px;
      color: #333;
    }

    .instruction-text small {
      font-size: 12px;
      color: #666;
    }

    .route-actions {
      display: flex;
      gap: 10px;
      margin-top: 15px;
    }

    .action-btn {
      flex: 1;
      padding: 10px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .share-btn {
      background: #17a2b8;
      color: white;
    }

    .clear-btn {
      background: #dc3545;
      color: white;
    }

    .action-btn:active {
      transform: scale(0.95);
    }

    /* Pulse animation for user location marker */
    @keyframes pulse {
      0% {
        box-shadow: 0 2px 6px rgba(0,0,0,0.3), 0 0 0 0 rgba(0, 123, 255, 0.7);
      }
      70% {
        box-shadow: 0 2px 6px rgba(0,0,0,0.3), 0 0 0 10px rgba(0, 123, 255, 0);
      }
      100% {
        box-shadow: 0 2px 6px rgba(0,0,0,0.3), 0 0 0 0 rgba(0, 123, 255, 0);
      }
    }

    /* Shuttle Screen Styles */
    .shuttle-content {
      padding: 15px;
      height: calc(100% - 60px);
      overflow-y: auto;
    }

    .shuttle-tabs {
      display: flex;
      margin-bottom: 20px;
      border-bottom: 2px solid #eee;
    }

    .shuttle-tabs .tab-btn {
      flex: 1;
      padding: 12px 8px;
      border: none;
      background: none;
      font-size: 12px;
      cursor: pointer;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
    }

    .shuttle-tabs .tab-btn.active {
      border-bottom-color: #FF6B35;
      color: #FF6B35;
      font-weight: 600;
    }

    .shuttle-tab-content {
      display: none;
    }

    .shuttle-tab-content.active {
      display: block;
    }

    .route-item, .stop-item, .vehicle-item {
      background: white;
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-left: 4px solid #FF6B35;
    }

    .route-header, .stop-header, .vehicle-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .route-name, .stop-name, .vehicle-name {
      font-weight: 600;
      color: #333;
      font-size: 16px;
    }

    .route-status, .stop-status, .vehicle-status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 600;
    }

    .status-active {
      background: #d4edda;
      color: #155724;
    }

    .status-delayed {
      background: #fff3cd;
      color: #856404;
    }

    .status-inactive {
      background: #f8d7da;
      color: #721c24;
    }

    .route-details, .stop-details, .vehicle-details {
      font-size: 14px;
      color: #666;
      line-height: 1.4;
    }

    .tracking-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }

    .map-toggle-btn {
      background: #17a2b8;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 12px;
      cursor: pointer;
    }

    /* Reports Screen Styles */
    .reports-content {
      padding: 15px;
      height: calc(100% - 60px);
      overflow-y: auto;
    }

    .reports-tabs {
      display: flex;
      margin-bottom: 20px;
      border-bottom: 2px solid #eee;
    }

    .reports-tabs .tab-btn {
      flex: 1;
      padding: 12px 8px;
      border: none;
      background: none;
      font-size: 12px;
      cursor: pointer;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
    }

    .reports-tabs .tab-btn.active {
      border-bottom-color: #FF6B35;
      color: #FF6B35;
      font-weight: 600;
    }

    .reports-tab-content {
      display: none;
    }

    .reports-tab-content.active {
      display: block;
    }

    .report-item {
      background: white;
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-left: 4px solid #FF6B35;
    }

    .report-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }

    .report-id {
      font-weight: 600;
      color: #333;
      font-size: 14px;
    }

    .report-status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 600;
    }

    .status-open {
      background: #fff3cd;
      color: #856404;
    }

    .status-in-progress {
      background: #d1ecf1;
      color: #0c5460;
    }

    .status-resolved {
      background: #d4edda;
      color: #155724;
    }

    .report-details {
      font-size: 14px;
      color: #666;
      line-height: 1.4;
    }

    .report-meta {
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      font-size: 12px;
      color: #888;
    }

    .report-actions {
      margin-top: 10px;
      display: flex;
      gap: 8px;
    }

    .action-btn-small {
      padding: 6px 12px;
      border: none;
      border-radius: 6px;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn-view {
      background: #17a2b8;
      color: white;
    }

    .btn-update {
      background: #ffc107;
      color: #212529;
    }

    .btn-contact {
      background: #28a745;
      color: white;
    }

    /* Crowd Information Screen Styles */
    .crowd-content {
      padding: 15px;
      height: calc(100% - 60px);
      overflow-y: auto;
    }

    .crowd-summary {
      margin-bottom: 25px;
    }

    .summary-stats {
      display: flex;
      gap: 10px;
      margin-top: 15px;
    }

    .stat-card {
      flex: 1;
      background: white;
      padding: 15px;
      border-radius: 12px;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .stat-number {
      font-size: 20px;
      font-weight: 700;
      color: #FF6B35;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 12px;
      color: #666;
    }

    .crowd-sectors {
      margin-bottom: 25px;
    }

    .sector-item {
      background: white;
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 10px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .sector-info {
      flex: 1;
    }

    .sector-name {
      font-weight: 600;
      color: #333;
      margin-bottom: 5px;
    }

    .sector-details {
      font-size: 12px;
      color: #666;
    }

    .density-indicator {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 11px;
      font-weight: 600;
      text-align: center;
      min-width: 60px;
    }

    .density-low {
      background: #d4edda;
      color: #155724;
    }

    .density-medium {
      background: #fff3cd;
      color: #856404;
    }

    .density-high {
      background: #f8d7da;
      color: #721c24;
    }

    .crowd-actions {
      display: flex;
      gap: 10px;
      margin-bottom: 25px;
    }

    .crowd-tips {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 12px;
    }

    .tips-list {
      margin-top: 10px;
    }

    .tip-item {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 8px 0;
      border-bottom: 1px solid #eee;
    }

    .tip-item:last-child {
      border-bottom: none;
    }

    .tip-icon {
      font-size: 16px;
      width: 24px;
      text-align: center;
    }

    .tip-text {
      font-size: 14px;
      color: #555;
    }



    /* Emergency Dashboard Styles */
    .emergency-header {
      background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    }

    .management-header {
      background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    }

    .emergency-dashboard, .management-dashboard {
      padding: 15px;
      height: calc(100% - 60px);
      overflow-y: auto;
    }

    .emergency-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 10px;
      margin-bottom: 20px;
    }

    .stat-card.urgent {
      background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
      color: white;
    }

    .stat-card.warning {
      background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
      color: #212529;
    }

    .stat-card.success {
      background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
      color: white;
    }

    .stat-card.info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
      color: white;
    }

    .emergency-tabs, .management-tabs {
      display: flex;
      margin-bottom: 20px;
      border-bottom: 2px solid #eee;
      overflow-x: auto;
    }

    .emergency-tabs .tab-btn, .management-tabs .tab-btn {
      flex: 1;
      min-width: 120px;
      padding: 12px 8px;
      border: none;
      background: none;
      font-size: 11px;
      cursor: pointer;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
      white-space: nowrap;
    }

    .emergency-tabs .tab-btn.active {
      border-bottom-color: #dc3545;
      color: #dc3545;
      font-weight: 600;
    }

    .management-tabs .tab-btn.active {
      border-bottom-color: #6f42c1;
      color: #6f42c1;
      font-weight: 600;
    }

    .emergency-tab-content, .management-tab-content {
      display: none;
    }

    .emergency-tab-content.active, .management-tab-content.active {
      display: block;
    }

    .incidents-header, .personnel-header, .resources-header, .comms-header,
    .users-controls, .analytics-controls, .logs-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding: 10px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .incident-item, .personnel-item, .resource-item, .user-item {
      background: white;
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 10px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-left: 4px solid #dc3545;
    }

    .incident-header, .personnel-header-item, .resource-header-item, .user-header-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .incident-priority, .personnel-status, .resource-status, .user-role {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 600;
    }

    .priority-high, .status-emergency {
      background: #f8d7da;
      color: #721c24;
    }

    .priority-medium, .status-busy {
      background: #fff3cd;
      color: #856404;
    }

    .priority-low, .status-available {
      background: #d4edda;
      color: #155724;
    }

    .system-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
    }

    .overview-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .health-indicators {
      margin-top: 15px;
    }

    .health-item {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 8px 0;
      border-bottom: 1px solid #eee;
    }

    .health-item:last-child {
      border-bottom: none;
    }

    .indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }

    .indicator.green {
      background: #28a745;
    }

    .indicator.yellow {
      background: #ffc107;
    }

    .indicator.red {
      background: #dc3545;
    }

    .analytics-summary {
      display: flex;
      justify-content: space-between;
      margin-top: 15px;
    }

    .metric {
      text-align: center;
    }

    .metric-value {
      display: block;
      font-size: 24px;
      font-weight: 700;
      color: #6f42c1;
    }

    .metric-label {
      font-size: 12px;
      color: #666;
    }

    .map-btn, .deploy-btn, .request-btn, .broadcast-btn, .action-btn {
      background: #17a2b8;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .deploy-btn {
      background: #28a745;
    }

    .request-btn {
      background: #ffc107;
      color: #212529;
    }

    .broadcast-btn {
      background: #dc3545;
    }

    .user-actions {
      display: flex;
      gap: 10px;
    }

    .log-filters {
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .clear-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 11px;
      cursor: pointer;
    }

    /* Missing Person Alert Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
    }

    .modal-content {
      background: white;
      border-radius: 16px;
      max-width: 90vw;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    }

    .modal-header {
      background: #FF6B35;
      color: white;
      padding: 20px;
      border-radius: 16px 16px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-header h3 {
      margin: 0;
      font-size: 18px;
    }

    .close-modal {
      background: none;
      border: none;
      color: white;
      font-size: 24px;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .modal-body {
      padding: 20px;
    }

    .missing-person-details {
      max-width: 500px;
    }

    .person-header {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 2px solid #eee;
    }

    .person-photo {
      width: 80px;
      height: 80px;
      border-radius: 12px;
      object-fit: cover;
      border: 3px solid #FF6B35;
    }

    .person-info h3 {
      margin: 0 0 5px 0;
      color: #333;
    }

    .person-info p {
      margin: 2px 0;
      color: #666;
    }

    .case-status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
    }

    .status-active {
      background: #f8d7da;
      color: #721c24;
    }

    .status-resolved {
      background: #d4edda;
      color: #155724;
    }

    .missing-person-details h4 {
      color: #FF6B35;
      margin: 15px 0 8px 0;
      font-size: 14px;
    }

    .missing-person-details p {
      margin: 5px 0;
      font-size: 13px;
      line-height: 1.4;
    }

    .missing-person-details ul {
      margin: 5px 0;
      padding-left: 20px;
    }

    .missing-person-details li {
      font-size: 13px;
      margin: 3px 0;
    }

    .search-actions {
      display: flex;
      gap: 10px;
      margin-top: 20px;
      flex-wrap: wrap;
    }

    .search-actions .action-btn {
      flex: 1;
      min-width: 120px;
      padding: 10px;
      font-size: 12px;
    }

    .sighting-form {
      max-width: 400px;
    }

    .sighting-form .form-group {
      margin-bottom: 15px;
    }

    .sighting-form label {
      display: block;
      margin-bottom: 5px;
      font-weight: 600;
      color: #333;
      font-size: 13px;
    }

    .sighting-form input,
    .sighting-form textarea,
    .sighting-form select {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 13px;
      box-sizing: border-box;
    }

    .sighting-form input[type="checkbox"] {
      width: auto;
      margin-right: 8px;
    }

    .form-actions {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }

    .submit-btn {
      background: #28a745;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 13px;
      flex: 1;
    }

    .cancel-btn {
      background: #6c757d;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 13px;
      flex: 1;
    }

    .missing-person-marker {
      background: #dc3545;
      color: white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      border: 3px solid white;
      box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4);
      animation: pulse-missing 2s infinite;
    }

    @keyframes pulse-missing {
      0% {
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4);
      }
      50% {
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.8);
        transform: scale(1.1);
      }
      100% {
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4);
      }
    }

    @media (max-width: 768px) {
      .modal-content {
        max-width: 95vw;
        margin: 10px;
      }

      .person-header {
        flex-direction: column;
        text-align: center;
      }

      .search-actions {
        flex-direction: column;
      }

      .search-actions .action-btn {
        min-width: auto;
      }
    }

    /* Missing Person Popup Styles */
    .missing-person-popup {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .popup-header {
      display: flex;
      gap: 10px;
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 2px solid #dc3545;
    }

    .popup-photo {
      width: 50px;
      height: 50px;
      border-radius: 8px;
      object-fit: cover;
      border: 2px solid #dc3545;
    }

    .popup-info h4 {
      margin: 0 0 5px 0;
      color: #dc3545;
      font-size: 14px;
      font-weight: 700;
    }

    .popup-info p {
      margin: 2px 0;
      font-size: 12px;
      color: #333;
    }

    .priority-critical {
      color: #dc3545 !important;
      font-weight: 700;
    }

    .priority-high {
      color: #fd7e14 !important;
      font-weight: 600;
    }

    .priority-medium {
      color: #ffc107 !important;
      font-weight: 600;
    }

    .popup-details {
      margin-bottom: 10px;
    }

    .popup-details p {
      margin: 3px 0;
      font-size: 11px;
      line-height: 1.3;
    }

    .popup-actions {
      display: flex;
      gap: 5px;
    }

    .popup-btn {
      flex: 1;
      padding: 6px 8px;
      border: none;
      border-radius: 6px;
      font-size: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .details-btn {
      background: #17a2b8;
      color: white;
    }

    .details-btn:hover {
      background: #138496;
    }

    .sighting-btn {
      background: #28a745;
      color: white;
    }

    .sighting-btn:hover {
      background: #1e7e34;
    }

    .missing-person-popup-container .leaflet-popup-content {
      margin: 8px;
      line-height: 1.3;
    }

    .missing-person-popup-container .leaflet-popup-content-wrapper {
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    }

    .missing-person-popup-container .leaflet-popup-tip {
      background: white;
    }

    /* AI Assistant Screen Styles */
    .ai-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .ai-assistant-container {
      display: flex;
      flex-direction: column;
      height: calc(100% - 60px);
      padding: 0;
    }

    .quick-topics {
      padding: 15px;
      background: #f8f9fa;
      border-bottom: 1px solid #eee;
    }

    .quick-topics h3 {
      margin: 0 0 15px 0;
      color: #333;
      font-size: 16px;
    }

    .topics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
      gap: 8px;
    }

    .topic-btn {
      background: white;
      border: 2px solid #667eea;
      color: #667eea;
      padding: 10px 8px;
      border-radius: 12px;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      font-weight: 600;
    }

    .topic-btn:hover {
      background: #667eea;
      color: white;
      transform: translateY(-2px);
    }

    .ai-chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 20px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }

    .welcome-message {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      animation: fadeInUp 0.6s ease;
    }

    .ai-avatar {
      width: 40px;
      height: 40px;
      background: #667eea;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      flex-shrink: 0;
    }

    .ai-message, .user-ai-message {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      animation: fadeInUp 0.4s ease;
    }

    .user-ai-message {
      flex-direction: row-reverse;
    }

    .user-ai-message .message-content {
      background: #667eea;
      color: white;
      margin-left: 40px;
      margin-right: 0;
    }

    .ai-message .message-content {
      background: white;
      margin-right: 40px;
    }

    .message-content {
      background: white;
      padding: 15px;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      max-width: calc(100% - 55px);
    }

    .message-content h4 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 16px;
    }

    .message-content p {
      margin: 8px 0;
      line-height: 1.5;
      font-size: 14px;
    }

    .message-content ul {
      margin: 10px 0;
      padding-left: 20px;
    }

    .message-content li {
      margin: 5px 0;
      font-size: 14px;
    }

    .ai-chat-input {
      padding: 15px;
      background: white;
      border-top: 1px solid #eee;
    }

    .input-container {
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .input-container input {
      flex: 1;
      padding: 12px 16px;
      border: 2px solid #e9ecef;
      border-radius: 25px;
      font-size: 14px;
      outline: none;
      transition: all 0.3s ease;
    }

    .input-container input:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .send-ai-btn {
      background: #667eea;
      border: none;
      color: white;
      padding: 12px 16px;
      border-radius: 50%;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s ease;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .send-ai-btn:hover {
      background: #5a6fd8;
      transform: scale(1.05);
    }

    .send-ai-btn:active {
      transform: scale(0.95);
    }

    .input-suggestions {
      margin-top: 10px;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .suggestion-chip {
      background: #e9ecef;
      border: none;
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .suggestion-chip:hover {
      background: #667eea;
      color: white;
    }

    .clear-chat-btn, .voice-btn {
      background: rgba(255,255,255,0.2);
      border: 1px solid rgba(255,255,255,0.3);
      color: white;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .clear-chat-btn:hover, .voice-btn:hover {
      background: rgba(255,255,255,0.3);
    }

    .voice-btn.active {
      background: #ff4757;
      border-color: #ff4757;
    }

    .typing-indicator {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      animation: fadeInUp 0.4s ease;
    }

    .typing-dots {
      background: white;
      padding: 15px;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      display: flex;
      gap: 4px;
      align-items: center;
    }

    .typing-dot {
      width: 8px;
      height: 8px;
      background: #667eea;
      border-radius: 50%;
      animation: typingDot 1.4s infinite ease-in-out;
    }

    .typing-dot:nth-child(2) {
      animation-delay: 0.2s;
    }

    .typing-dot:nth-child(3) {
      animation-delay: 0.4s;
    }

    @keyframes typingDot {
      0%, 60%, 100% {
        transform: scale(1);
        opacity: 0.5;
      }
      30% {
        transform: scale(1.2);
        opacity: 1;
      }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @media (max-width: 768px) {
      .topics-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      }

      .topic-btn {
        font-size: 10px;
        padding: 8px 6px;
      }

      .ai-chat-messages {
        padding: 15px;
      }

      .message-content {
        max-width: calc(100% - 45px);
      }

      .ai-message .message-content {
        margin-right: 30px;
      }

      .user-ai-message .message-content {
        margin-left: 30px;
      }
    }

    .sector-marker {
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .sector-marker:hover {
      transform: scale(1.1);
    }

    .user-marker {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
    }

    .facilities-popup .leaflet-popup-content {
      margin: 0;
      padding: 0;
    }

    .leaflet-popup-content-wrapper {
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    }

    .leaflet-popup-tip {
      background: white;
    }

    /* Religious theme colors */
    .controls h3 {
      text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .dashboard-header {
      background: linear-gradient(135deg, #FF6B35, #F7931E);
      text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    }

    /* Loading animation for forms */
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
    }

    /* Success message styling */
    .success-message {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: white;
      padding: 10px;
      border-radius: 6px;
      margin: 10px 0;
      text-align: center;
      animation: slideIn 0.3s ease;
    }

    @keyframes slideIn {
      from { transform: translateY(-20px); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }

    /* Enhanced button styles */
    button {
      transition: all 0.3s ease;
    }

    button:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    button:active {
      transform: translateY(0);
    }

    /* Heat Map Legend */
    .heat-map-legend {
      position: absolute;
      bottom: 100px;
      right: 10px;
      background: white;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      z-index: 1000;
      font-size: 12px;
      display: none;
    }

    .heat-map-legend h4 {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #333;
    }

    .legend-item {
      display: flex;
      align-items: center;
      margin: 5px 0;
    }

    .legend-color {
      width: 20px;
      height: 12px;
      margin-right: 8px;
      border-radius: 2px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .dashboard-grid {
        padding: 15px;
        gap: 12px;
      }

      .dashboard-card {
        padding: 15px;
        min-height: 100px;
      }

      .dashboard-card .card-icon {
        font-size: 28px;
      }

      .dashboard-card h3 {
        font-size: 14px;
      }

      .dashboard-card p {
        font-size: 11px;
      }

      .screen-header {
        padding: 12px 15px;
      }

      .screen-header h2 {
        font-size: 16px;
      }

      .category-tabs {
        padding: 10px 15px 0;
        gap: 8px;
      }

      .tab-btn {
        padding: 6px 12px;
        font-size: 12px;
      }

      .form-container {
        padding: 15px;
      }

      .mobile-form {
        padding: 15px;
      }

      .heat-map-legend {
        bottom: 80px;
        right: 5px;
        left: 5px;
        font-size: 11px;
      }

      .map-controls {
        padding: 12px;
      }

      .control-group select {
        padding: 10px;
        font-size: 14px;
      }

      .primary-btn, .secondary-btn {
        padding: 10px;
        font-size: 14px;
      }
    }

    @media (max-width: 480px) {
      .dashboard-header h1 {
        font-size: 20px;
      }

      .dashboard-header p {
        font-size: 14px;
      }

      .location-status {
        font-size: 12px;
        padding: 6px 12px;
      }

      .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 10px;
      }

      .dashboard-card {
        min-height: 80px;
        padding: 12px;
      }

      .dashboard-card .card-icon {
        font-size: 24px;
        margin-bottom: 8px;
      }

      .control-buttons {
        flex-direction: column;
        gap: 8px;
      }
    }

    /* Touch and Accessibility Improvements */
    @media (hover: none) and (pointer: coarse) {
      .dashboard-card:hover {
        transform: none;
      }

      .dashboard-card:active {
        transform: scale(0.95);
        background: #f8f9fa;
      }

      button:hover {
        transform: none;
      }

      button:active {
        transform: scale(0.95);
      }
    }

    /* Prevent zoom on input focus for iOS */
    @media screen and (-webkit-min-device-pixel-ratio: 0) {
      select, textarea, input[type="text"], input[type="password"],
      input[type="datetime"], input[type="datetime-local"],
      input[type="date"], input[type="month"], input[type="time"],
      input[type="week"], input[type="number"], input[type="email"],
      input[type="url"], input[type="search"], input[type="tel"],
      input[type="color"] {
        font-size: 16px;
      }
    }
  </style>
</head>
<body>
  <!-- Main App Container -->
  <div id="app" class="app-container">

    <!-- Main Dashboard Screen -->
    <div id="dashboard-screen" class="screen active">
      <div class="dashboard-header">
        <h1>🕉️ Kumbh Mela 2028</h1>
        <p>Triveni Ghat, Ujjain</p>
        <div class="location-status" id="locationStatus">
          <span class="location-icon">📍</span>
          <span id="locationText">Getting location...</span>
        </div>
      </div>

      <div class="dashboard-grid">
        <div class="dashboard-card" onclick="showScreen('map-screen')">
          <div class="card-icon">🗺️</div>
          <h3>Map View</h3>
          <p>Navigate to sectors and facilities</p>
        </div>

        <div class="dashboard-card" onclick="showScreen('nearby-screen')">
          <div class="card-icon">📍</div>
          <h3>Nearby Places</h3>
          <p>Find facilities and attractions</p>
        </div>

        <div class="dashboard-card" onclick="showScreen('shuttle-screen')">
          <div class="card-icon">🚌</div>
          <h3>Shuttle Routes</h3>
          <p>Real-time shuttle tracking</p>
        </div>

        <div class="dashboard-card" onclick="showScreen('emergency-screen')">
          <div class="card-icon">🚨</div>
          <h3>Emergency</h3>
          <p>Report emergencies quickly</p>
        </div>

        <div class="dashboard-card" onclick="showScreen('reports-screen')">
          <div class="card-icon">📋</div>
          <h3>Reports List</h3>
          <p>View all submitted reports</p>
        </div>

        <div class="dashboard-card" onclick="showScreen('crowd-screen')">
          <div class="card-icon">👥</div>
          <h3>Crowd Info</h3>
          <p>Real-time crowd density</p>
        </div>

        <div class="dashboard-card" onclick="showScreen('ai-assistant-screen')">
          <div class="card-icon">🤖</div>
          <h3>AI Assistant</h3>
          <p>Comprehensive Kumbh Mela guide</p>
        </div>

        <div class="dashboard-card admin-card" onclick="showScreen('police-dashboard')">
          <div class="card-icon">🚔</div>
          <h3>Emergency Control</h3>
          <p>Police & Emergency Dashboard</p>
        </div>

        <div class="dashboard-card admin-card" onclick="showScreen('management-dashboard')">
          <div class="card-icon">⚙️</div>
          <h3>Management</h3>
          <p>System Administration</p>
        </div>

        <div class="dashboard-card" onclick="showScreen('missing-screen')">
          <div class="card-icon">🔍</div>
          <h3>Missing Person</h3>
          <p>Report missing persons/items</p>
        </div>

        <div class="dashboard-card" onclick="showScreen('found-screen')">
          <div class="card-icon">📦</div>
          <h3>Found Items</h3>
          <p>Report found items</p>
        </div>

        <div class="dashboard-card" onclick="showCrowdInfo()">
          <div class="card-icon">👥</div>
          <h3>Crowd Info</h3>
          <p>Check crowd density</p>
        </div>
      </div>
    </div>

    <!-- Map Screen -->
    <div id="map-screen" class="screen">
      <div class="screen-header">
        <button class="back-btn" onclick="showScreen('dashboard-screen')" id="mapBackBtn">← Dashboard</button>
        <h2>Map Navigation</h2>
        <div class="header-controls">
          <button class="heat-toggle" onclick="toggleHeatMap()" id="heatToggle">🔥 Heat Map</button>
          <button class="locations-toggle" onclick="toggleNearbyMarkers()" id="locationsToggle">📍 Locations</button>
        </div>
      </div>
      <div id="map"></div>

      <!-- Map Controls -->
      <div class="map-controls">
        <div class="control-group">
          <label for="toSector">Select Destination:</label>
          <select id="toSector">
            <option value="triveniGhat">Triveni Ghat (Main)</option>
            <option value="sector1">North Sector - Main Entrance</option>
            <option value="sector2">East Sector - Bathing Ghats</option>
            <option value="sector3">South Sector - Parking & Transport</option>
            <option value="sector4">West Sector - Accommodation</option>
            <option value="sector5">Northeast Sector - Commercial</option>
            <option value="sector6">Southwest Sector - Services</option>
          </select>
        </div>
        <div class="control-buttons">
          <button id="navigateBtn" class="primary-btn">🧭 Navigate</button>
          <button id="findLocationBtn" class="secondary-btn">📍 My Location</button>
        </div>
      </div>
    </div>

    <!-- Nearby Places Screen -->
    <div id="nearby-screen" class="screen">
      <div class="screen-header">
        <button class="back-btn" onclick="showScreen('dashboard-screen')">← Back</button>
        <h2>Nearby Places</h2>
        <button class="refresh-btn" onclick="refreshNearbyPlaces()">�</button>
      </div>

      <div class="category-tabs">
        <button class="tab-btn active" onclick="showCategory('all', this)">All</button>
        <button class="tab-btn" onclick="showCategory('washroom', this)">🚻 Washrooms</button>
        <button class="tab-btn" onclick="showCategory('medical', this)">🏥 Medical</button>
        <button class="tab-btn" onclick="showCategory('police_station', this)">🚔 Police</button>
        <button class="tab-btn" onclick="showCategory('food', this)">🍽️ Food</button>
        <button class="tab-btn" onclick="showCategory('mandir', this)">🛕 Temples</button>
        <button class="tab-btn" onclick="showCategory('parking', this)">🅿️ Parking</button>
        <button class="tab-btn" onclick="showCategory('rest', this)">🏕️ Rest</button>
      </div>

      <div class="places-list" id="placesList">
        <!-- Places will be populated dynamically -->
      </div>
    </div>

    <!-- Shuttle Routes Screen -->
    <div id="shuttle-screen" class="screen">
      <div class="screen-header">
        <button class="back-btn" onclick="showScreen('dashboard-screen')">← Back</button>
        <h2>Shuttle Routes</h2>
        <button class="refresh-btn" onclick="refreshShuttleData()">🔄</button>
      </div>

      <div class="shuttle-content">
        <div class="shuttle-tabs">
          <button class="tab-btn active" onclick="showShuttleTab('routes', this)">🚌 Routes</button>
          <button class="tab-btn" onclick="showShuttleTab('stops', this)">🚏 Stops</button>
          <button class="tab-btn" onclick="showShuttleTab('live', this)">📍 Live Tracking</button>
        </div>

        <div id="shuttle-routes" class="shuttle-tab-content active">
          <div class="route-list" id="routesList">
            <!-- Routes will be populated here -->
          </div>
        </div>

        <div id="shuttle-stops" class="shuttle-tab-content">
          <div class="stops-list" id="stopsList">
            <!-- Stops will be populated here -->
          </div>
        </div>

        <div id="shuttle-live" class="shuttle-tab-content">
          <div class="live-tracking" id="liveTracking">
            <div class="tracking-header">
              <h4>🚌 Live Shuttle Locations</h4>
              <button onclick="toggleShuttleMap()" class="map-toggle-btn">📍 Show on Map</button>
            </div>
            <div class="shuttle-vehicles" id="shuttleVehicles">
              <!-- Live shuttle data will be populated here -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Emergency Report Screen -->
    <div id="emergency-screen" class="screen">
      <div class="screen-header">
        <button class="back-btn" onclick="showScreen('dashboard-screen')">← Back</button>
        <h2>🚨 Emergency Report</h2>
      </div>
      <div class="form-container">
        <form id="emergencyForm" class="mobile-form">
          <div class="form-group">
            <label for="emergency-type">Emergency Type:</label>
            <select id="emergency-type" required>
              <option value="">Select Emergency Type</option>
              <option value="medical">🏥 Medical Emergency</option>
              <option value="fire">🚒 Fire Incident</option>
              <option value="police">🚔 Security Issue</option>
              <option value="accident">🚗 Accident</option>
              <option value="other">❓ Other</option>
            </select>
          </div>
          <div class="form-group">
            <label for="emergency-description">Description:</label>
            <textarea id="emergency-description" rows="4" placeholder="Describe the emergency situation..." required></textarea>
          </div>
          <div class="form-group">
            <label for="emergency-priority">Priority Level:</label>
            <select id="emergency-priority">
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>
          <button type="submit" class="submit-btn">🚨 Submit Emergency Report</button>
        </form>
      </div>
    </div>

    <!-- Reports List Screen -->
    <div id="reports-screen" class="screen">
      <div class="screen-header">
        <button class="back-btn" onclick="showScreen('dashboard-screen')">← Back</button>
        <h2>📋 Reports List</h2>
        <button class="refresh-btn" onclick="refreshReports()">🔄</button>
      </div>

      <div class="reports-content">
        <div class="reports-tabs">
          <button class="tab-btn active" onclick="showReportsTab('emergency', this)">🚨 Emergency</button>
          <button class="tab-btn" onclick="showReportsTab('missing', this)">👤 Missing</button>
          <button class="tab-btn" onclick="showReportsTab('found', this)">📦 Found Items</button>
        </div>

        <div id="emergency-reports" class="reports-tab-content active">
          <div class="reports-list" id="emergencyReportsList">
            <!-- Emergency reports will be populated here -->
          </div>
        </div>

        <div id="missing-reports" class="reports-tab-content">
          <div class="reports-list" id="missingReportsList">
            <!-- Missing person reports will be populated here -->
          </div>
        </div>

        <div id="found-reports" class="reports-tab-content">
          <div class="reports-list" id="foundReportsList">
            <!-- Found items reports will be populated here -->
          </div>
        </div>
      </div>
    </div>

    <!-- Crowd Information Screen -->
    <div id="crowd-screen" class="screen">
      <div class="screen-header">
        <button class="back-btn" onclick="showScreen('dashboard-screen')">← Back</button>
        <h2>👥 Crowd Information</h2>
        <button class="refresh-btn" onclick="refreshCrowdData()">🔄</button>
      </div>

      <div class="crowd-content">
        <div class="crowd-summary">
          <h3>📊 Current Status</h3>
          <div class="summary-stats">
            <div class="stat-card">
              <div class="stat-number" id="totalVisitors">25,847</div>
              <div class="stat-label">Total Visitors</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" id="currentDensity">Medium</div>
              <div class="stat-label">Overall Density</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" id="peakTime">6:00 PM</div>
              <div class="stat-label">Expected Peak</div>
            </div>
          </div>
        </div>

        <div class="crowd-sectors">
          <h3>🗺️ Sector-wise Density</h3>
          <div class="sectors-list" id="crowdSectorsList">
            <!-- Crowd data will be populated here -->
          </div>
        </div>

        <div class="crowd-actions">
          <button onclick="showCrowdMap()" class="action-btn primary-btn">
            🗺️ View on Map
          </button>
          <button onclick="getCrowdAlerts()" class="action-btn secondary-btn">
            🔔 Get Alerts
          </button>
        </div>

        <div class="crowd-tips">
          <h3>💡 Tips for Better Experience</h3>
          <div class="tips-list">
            <div class="tip-item">
              <span class="tip-icon">⏰</span>
              <span class="tip-text">Visit early morning (5-7 AM) for less crowds</span>
            </div>
            <div class="tip-item">
              <span class="tip-icon">🚶</span>
              <span class="tip-text">Use alternative routes during peak hours</span>
            </div>
            <div class="tip-item">
              <span class="tip-icon">📱</span>
              <span class="tip-text">Keep this app handy for real-time updates</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Police/Emergency Dashboard -->
    <div id="police-dashboard" class="screen">
      <div class="screen-header emergency-header">
        <button class="back-btn" onclick="showScreen('dashboard-screen')">← Back</button>
        <h2>🚔 Emergency Control Center</h2>
        <div class="header-controls">
          <button class="alert-btn" onclick="toggleEmergencyAlerts()" id="alertToggle">🔔 Alerts</button>
          <button class="refresh-btn" onclick="refreshEmergencyData()">🔄</button>
        </div>
      </div>

      <div class="emergency-dashboard">
        <!-- Emergency Stats -->
        <div class="emergency-stats">
          <div class="stat-card urgent">
            <div class="stat-number" id="activeIncidents">7</div>
            <div class="stat-label">Active Incidents</div>
          </div>
          <div class="stat-card warning">
            <div class="stat-number" id="pendingReports">12</div>
            <div class="stat-label">Pending Reports</div>
          </div>
          <div class="stat-card success">
            <div class="stat-number" id="resolvedToday">23</div>
            <div class="stat-label">Resolved Today</div>
          </div>
          <div class="stat-card info">
            <div class="stat-number" id="personnelOnDuty">45</div>
            <div class="stat-label">Personnel On Duty</div>
          </div>
        </div>

        <!-- Emergency Tabs -->
        <div class="emergency-tabs">
          <button class="tab-btn active" onclick="showEmergencyTab('incidents', this)">🚨 Active Incidents</button>
          <button class="tab-btn" onclick="showEmergencyTab('personnel', this)">👮 Personnel</button>
          <button class="tab-btn" onclick="showEmergencyTab('resources', this)">🚑 Resources</button>
          <button class="tab-btn" onclick="showEmergencyTab('communications', this)">📡 Comms</button>
        </div>

        <!-- Active Incidents Tab -->
        <div id="emergency-incidents" class="emergency-tab-content active">
          <div class="incidents-header">
            <h3>🚨 Active Emergency Incidents</h3>
            <button onclick="showEmergencyMap()" class="map-btn">🗺️ View on Map</button>
          </div>
          <div class="incidents-list" id="activeIncidentsList">
            <!-- Active incidents will be populated here -->
          </div>
        </div>

        <!-- Personnel Tab -->
        <div id="emergency-personnel" class="emergency-tab-content">
          <div class="personnel-header">
            <h3>👮 Personnel Deployment</h3>
            <button onclick="deployPersonnel()" class="deploy-btn">➕ Deploy</button>
          </div>
          <div class="personnel-list" id="personnelList">
            <!-- Personnel data will be populated here -->
          </div>
        </div>

        <!-- Resources Tab -->
        <div id="emergency-resources" class="emergency-tab-content">
          <div class="resources-header">
            <h3>🚑 Emergency Resources</h3>
            <button onclick="requestResources()" class="request-btn">📞 Request</button>
          </div>
          <div class="resources-list" id="resourcesList">
            <!-- Resources data will be populated here -->
          </div>
        </div>

        <!-- Communications Tab -->
        <div id="emergency-communications" class="emergency-tab-content">
          <div class="comms-header">
            <h3>📡 Communications Center</h3>
            <button onclick="broadcastAlert()" class="broadcast-btn">📢 Broadcast</button>
          </div>
          <div class="comms-panel" id="commsPanel">
            <!-- Communications interface will be populated here -->
          </div>
        </div>
      </div>
    </div>

    <!-- Management Dashboard -->
    <div id="management-dashboard" class="screen">
      <div class="screen-header management-header">
        <button class="back-btn" onclick="showScreen('dashboard-screen')">← Back</button>
        <h2>⚙️ Management Dashboard</h2>
        <div class="header-controls">
          <button class="settings-btn" onclick="openSettings()">⚙️ Settings</button>
          <button class="refresh-btn" onclick="refreshManagementData()">🔄</button>
        </div>
      </div>

      <div class="management-dashboard">
        <!-- System Overview -->
        <div class="system-overview">
          <div class="overview-card">
            <h3>📊 System Health</h3>
            <div class="health-indicators">
              <div class="health-item">
                <span class="indicator green"></span>
                <span>API Services</span>
                <span class="status">Online</span>
              </div>
              <div class="health-item">
                <span class="indicator green"></span>
                <span>Database</span>
                <span class="status">Healthy</span>
              </div>
              <div class="health-item">
                <span class="indicator yellow"></span>
                <span>Map Services</span>
                <span class="status">Slow</span>
              </div>
              <div class="health-item">
                <span class="indicator green"></span>
                <span>Notifications</span>
                <span class="status">Active</span>
              </div>
            </div>
          </div>

          <div class="overview-card">
            <h3>📈 Usage Analytics</h3>
            <div class="analytics-summary">
              <div class="metric">
                <span class="metric-value">2,847</span>
                <span class="metric-label">Active Users</span>
              </div>
              <div class="metric">
                <span class="metric-value">156</span>
                <span class="metric-label">Reports Today</span>
              </div>
              <div class="metric">
                <span class="metric-value">98.7%</span>
                <span class="metric-label">Uptime</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Management Tabs -->
        <div class="management-tabs">
          <button class="tab-btn active" onclick="showManagementTab('users', this)">👥 Users</button>
          <button class="tab-btn" onclick="showManagementTab('analytics', this)">📊 Analytics</button>
          <button class="tab-btn" onclick="showManagementTab('settings', this)">⚙️ Settings</button>
          <button class="tab-btn" onclick="showManagementTab('logs', this)">📝 Logs</button>
        </div>

        <!-- Users Tab -->
        <div id="management-users" class="management-tab-content active">
          <div class="users-controls">
            <h3>👥 User Management</h3>
            <div class="user-actions">
              <button onclick="addUser()" class="action-btn">➕ Add User</button>
              <button onclick="exportUsers()" class="action-btn">📤 Export</button>
            </div>
          </div>
          <div class="users-list" id="usersList">
            <!-- Users data will be populated here -->
          </div>
        </div>

        <!-- Analytics Tab -->
        <div id="management-analytics" class="management-tab-content">
          <div class="analytics-controls">
            <h3>📊 System Analytics</h3>
            <select id="analyticsTimeframe">
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
            </select>
          </div>
          <div class="analytics-content" id="analyticsContent">
            <!-- Analytics charts and data will be populated here -->
          </div>
        </div>

        <!-- Settings Tab -->
        <div id="management-settings" class="management-tab-content">
          <div class="settings-content">
            <h3>⚙️ System Configuration</h3>
            <div class="settings-groups" id="settingsGroups">
              <!-- Settings will be populated here -->
            </div>
          </div>
        </div>

        <!-- Logs Tab -->
        <div id="management-logs" class="management-tab-content">
          <div class="logs-controls">
            <h3>📝 System Logs</h3>
            <div class="log-filters">
              <select id="logLevel">
                <option value="all">All Levels</option>
                <option value="error">Errors</option>
                <option value="warning">Warnings</option>
                <option value="info">Info</option>
              </select>
              <button onclick="clearLogs()" class="clear-btn">🗑️ Clear</button>
            </div>
          </div>
          <div class="logs-content" id="logsContent">
            <!-- Logs will be populated here -->
          </div>
        </div>
      </div>
    </div>

    <!-- AI Assistant Screen -->
    <div id="ai-assistant-screen" class="screen">
      <div class="screen-header ai-header">
        <button class="back-btn" onclick="showScreen('dashboard-screen')">← Back</button>
        <h2>🤖 Kumbh Mela AI Assistant</h2>
        <div class="header-controls">
          <button class="clear-chat-btn" onclick="clearChatHistory()">🗑️ Clear</button>
          <button class="voice-btn" onclick="toggleVoiceMode()" id="voiceBtn">🎤 Voice</button>
        </div>
      </div>

      <div class="ai-assistant-container">
        <!-- Quick Topics -->
        <div class="quick-topics" id="quickTopics">
          <h3>🔥 Popular Topics</h3>
          <div class="topics-grid">
            <button class="topic-btn" onclick="askAI('Kumbh Mela 2028 dates and significance')">
              📅 Kumbh Mela 2028
            </button>
            <button class="topic-btn" onclick="askAI('Aarti timings at all temples')">
              🕯️ Aarti Timings
            </button>
            <button class="topic-btn" onclick="askAI('Best accommodation options with prices')">
              🏨 Accommodation
            </button>
            <button class="topic-btn" onclick="askAI('Transportation and taxi fares')">
              🚗 Transportation
            </button>
            <button class="topic-btn" onclick="askAI('Food recommendations and safety')">
              🍽️ Food & Dining
            </button>
            <button class="topic-btn" onclick="askAI('Shopping and local markets')">
              🛍️ Shopping
            </button>
            <button class="topic-btn" onclick="askAI('Cultural significance and rituals')">
              🕉️ Cultural Info
            </button>
            <button class="topic-btn" onclick="askAI('Safety guidelines and emergency contacts')">
              🛡️ Safety & Emergency
            </button>
          </div>
        </div>

        <!-- Chat Messages -->
        <div class="ai-chat-messages" id="aiChatMessages">
          <div class="welcome-message">
            <div class="ai-avatar">🤖</div>
            <div class="message-content">
              <h4>🙏 Namaste! Welcome to Kumbh Mela 2028 AI Assistant</h4>
              <p>I'm your comprehensive guide for the Kumbh Mela at Triveni Ghat, Ujjain. I can help you with:</p>
              <ul>
                <li>🕯️ Detailed aarti timings and temple schedules</li>
                <li>🏨 Accommodation options with real prices</li>
                <li>🚗 Transportation routes and fare information</li>
                <li>🍽️ Food recommendations and safety tips</li>
                <li>🛍️ Shopping guides and local markets</li>
                <li>🕉️ Cultural significance and proper rituals</li>
                <li>📱 Practical information (ATMs, mobile networks, etc.)</li>
              </ul>
              <p>Ask me anything about your Kumbh Mela journey!</p>
            </div>
          </div>
        </div>

        <!-- Chat Input -->
        <div class="ai-chat-input">
          <div class="input-container">
            <input type="text" id="aiChatInput" placeholder="Ask me anything about Kumbh Mela..." onkeypress="handleAIChatKeypress(event)">
            <button onclick="sendAIMessage()" class="send-ai-btn">
              <span id="sendIcon">📤</span>
            </button>
          </div>
          <div class="input-suggestions" id="inputSuggestions">
            <!-- Dynamic suggestions will appear here -->
          </div>
        </div>
      </div>
    </div>

    <!-- Missing Person Screen -->
    <div id="missing-screen" class="screen">
      <div class="screen-header">
        <button class="back-btn" onclick="showScreen('dashboard-screen')">← Back</button>
        <h2>🔍 Missing Person/Item</h2>
      </div>
      <div class="form-container">
        <form id="missingForm" class="mobile-form">
          <div class="form-group">
            <label for="missing-name">Name/Item:</label>
            <input type="text" id="missing-name" placeholder="Enter name or item description" required>
          </div>
          <div class="form-group">
            <label for="missing-age">Age (if person):</label>
            <input type="number" id="missing-age" placeholder="Age in years">
          </div>
          <div class="form-group">
            <label for="missing-gender">Gender (if person):</label>
            <select id="missing-gender">
              <option value="">Select Gender</option>
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="other">Other</option>
            </select>
          </div>
          <div class="form-group">
            <label for="missing-description">Description:</label>
            <textarea id="missing-description" rows="4" placeholder="Physical description, clothing, etc..." required></textarea>
          </div>
          <div class="form-group">
            <label for="missing-contact">Contact Information:</label>
            <input type="tel" id="missing-contact" placeholder="Your contact number" required>
          </div>
          <button type="submit" class="submit-btn">🔍 Submit Missing Report</button>
        </form>
      </div>
    </div>

    <!-- Found Items Screen -->
    <div id="found-screen" class="screen">
      <div class="screen-header">
        <button class="back-btn" onclick="showScreen('dashboard-screen')">← Back</button>
        <h2>📦 Found Items</h2>
      </div>
      <div class="form-container">
        <form id="foundForm" class="mobile-form">
          <div class="form-group">
            <label for="found-item">Item Name:</label>
            <input type="text" id="found-item" placeholder="What did you find?" required>
          </div>
          <div class="form-group">
            <label for="found-description">Description:</label>
            <textarea id="found-description" rows="4" placeholder="Detailed description of the found item..." required></textarea>
          </div>
          <div class="form-group">
            <label for="found-contact">Your Contact Details:</label>
            <input type="text" id="found-contact" placeholder="Phone number or other contact info" required>
          </div>
          <button type="submit" class="submit-btn">📦 Submit Found Item Report</button>
        </form>
      </div>
    </div>

  </div>



  <!-- Loading Overlay -->
  <div id="loadingOverlay" class="loading-overlay">
    <div class="loading-spinner"></div>
    <p>Loading...</p>
  </div>

  <!-- Toast Notifications -->
  <div id="toastContainer" class="toast-container"></div>

  <!-- Old Dashboard (keeping for compatibility) -->
  <div class="dashboard" id="dashboard">
    <div class="dashboard-header" onclick="toggleDashboard()">
      <span>🚨 Kumbh Mela Services</span>
      <span id="dashboardToggle">▼</span>
    </div>
    <div class="dashboard-content">
      <div class="form-tabs">
        <button class="tab-button active" onclick="showForm('emergency', this)">🚨 Emergency</button>
        <button class="tab-button" onclick="showForm('missing', this)">🔍 Missing</button>
        <button class="tab-button" onclick="showForm('found', this)">📦 Found</button>
      </div>

      <!-- Emergency Report Form -->
      <div id="emergency-form" class="form-content active">
        <h4>Emergency Report</h4>
        <div class="form-group">
          <label for="emergency-image">Upload Image:</label>
          <input type="file" id="emergency-image" accept="image/*">
        </div>
        <div class="form-group">
          <label for="emergency-reason">Emergency Type:</label>
          <select id="emergency-reason">
            <option value="">Select Emergency Type</option>
            <option value="medical">Medical Emergency</option>
            <option value="fire">Fire Incident</option>
            <option value="security">Security Issue</option>
            <option value="crowd">Crowd Control</option>
            <option value="accident">Accident</option>
            <option value="other">Other</option>
          </select>
        </div>
        <div class="form-group">
          <label for="emergency-remarks">Description/Remarks:</label>
          <textarea id="emergency-remarks" rows="3" placeholder="Describe the emergency situation..."></textarea>
        </div>
        <div class="form-group">
          <label for="emergency-assign">Assign to:</label>
          <select id="emergency-assign">
            <option value="police">🚔 Police</option>
            <option value="medical">🏥 Medical</option>
            <option value="fire">🚒 Fire Department</option>
          </select>
        </div>
        <button class="submit-btn" onclick="submitEmergencyReport()">Submit Emergency Report</button>
      </div>

      <!-- Missing Person/Item Form -->
      <div id="missing-form" class="form-content">
        <h4>Missing Person/Item Report</h4>
        <div class="form-group">
          <label for="missing-name">Name/Item:</label>
          <input type="text" id="missing-name" placeholder="Enter name or item description">
        </div>
        <div class="form-group">
          <label for="missing-image">Upload Photo:</label>
          <input type="file" id="missing-image" accept="image/*">
        </div>
        <div class="form-group">
          <label for="missing-location">Last Seen Location:</label>
          <select id="missing-location">
            <option value="">Select Location</option>
            <option value="triveniGhat">Triveni Ghat (Main)</option>
            <option value="sector1">North Sector - Main Entrance</option>
            <option value="sector2">East Sector - Bathing Ghats</option>
            <option value="sector3">South Sector - Parking & Transport</option>
            <option value="sector4">West Sector - Accommodation</option>
            <option value="sector5">Northeast Sector - Commercial</option>
            <option value="sector6">Southwest Sector - Services</option>
          </select>
        </div>
        <div class="form-group">
          <label for="missing-remarks">Additional Details:</label>
          <textarea id="missing-remarks" rows="3" placeholder="Physical description, clothing, etc..."></textarea>
        </div>
        <div class="form-group">
          <label for="missing-contact">Contact Phone:</label>
          <input type="tel" id="missing-contact" placeholder="Enter contact number">
        </div>
        <button class="submit-btn" onclick="submitMissingReport()">Submit Missing Report</button>
      </div>

      <!-- Found Items Form -->
      <div id="found-form" class="form-content">
        <h4>Found Items Report</h4>
        <div class="form-group">
          <label for="found-area">Nearest Area/Sector:</label>
          <select id="found-area">
            <option value="">Select Area</option>
            <option value="triveniGhat">Triveni Ghat (Main)</option>
            <option value="sector1">North Sector - Main Entrance</option>
            <option value="sector2">East Sector - Bathing Ghats</option>
            <option value="sector3">South Sector - Parking & Transport</option>
            <option value="sector4">West Sector - Accommodation</option>
            <option value="sector5">Northeast Sector - Commercial</option>
            <option value="sector6">Southwest Sector - Services</option>
          </select>
        </div>
        <div class="form-group">
          <label for="found-item">Item Name:</label>
          <input type="text" id="found-item" placeholder="What did you find?">
        </div>
        <div class="form-group">
          <label for="found-description">Description:</label>
          <textarea id="found-description" rows="3" placeholder="Detailed description of the found item..."></textarea>
        </div>
        <div class="form-group">
          <label for="found-contact">Your Contact Details:</label>
          <input type="text" id="found-contact" placeholder="Phone number or other contact info">
        </div>
        <button class="submit-btn" onclick="submitFoundReport()">Submit Found Item Report</button>
      </div>
    </div>
  </div>

  <!-- Route Information Panel -->
  <div class="route-info" id="routeInfo">
    <h4 style="margin: 0 0 10px 0; color: #FF6B35;">Route Information</h4>
    <div id="routeDetails"></div>
  </div>

  <!-- Leaflet JS -->
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
  <script src="https://unpkg.com/leaflet-routing-machine/dist/leaflet-routing-machine.js"></script>
  <script src="https://unpkg.com/leaflet.heat/dist/leaflet-heat.js"></script>

  <!-- API Service -->
  <script src="api-service.js"></script>

  <!-- Nearby Places Data -->
  <script src="nearby-places-data.js"></script>

  <!-- Missing Persons Data -->
  <script src="missing-persons-data.js"></script>

  <!-- App Scripts -->
  <script src="app.js"></script>
  <script src="sectors.js"></script>

</body>
</html>