# =============================================================================
# SIMHASTHA KUMBH MELA 2028 - GIT IGNORE FILE
# =============================================================================

# -----------------------------------------------------------------------------
# ENVIRONMENT AND SECRETS
# -----------------------------------------------------------------------------
# Environment variables and configuration files
.env
.env.local
.env.development
.env.staging
.env.production
.env.test

# Configuration files with secrets
config.json
secrets.json
credentials.json

# -----------------------------------------------------------------------------
# PYTHON
# -----------------------------------------------------------------------------
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# -----------------------------------------------------------------------------
# NODE.JS
# -----------------------------------------------------------------------------
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# -----------------------------------------------------------------------------
# DATABASES
# -----------------------------------------------------------------------------
# SQLite databases
*.db
*.sqlite
*.sqlite3

# Database dumps
*.sql
*.dump

# -----------------------------------------------------------------------------
# LOGS AND TEMPORARY FILES
# -----------------------------------------------------------------------------
# Log files
*.log
logs/
log/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# -----------------------------------------------------------------------------
# IDE AND EDITOR FILES
# -----------------------------------------------------------------------------
# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# -----------------------------------------------------------------------------
# DEPLOYMENT AND DOCKER
# -----------------------------------------------------------------------------
# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# -----------------------------------------------------------------------------
# MEDIA AND UPLOADS
# -----------------------------------------------------------------------------
# User uploaded files
uploads/
media/
static/media/

# Image files (if not part of the project)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.ico

# Video files
*.mp4
*.avi
*.mov
*.wmv
*.flv

# Audio files
*.mp3
*.wav
*.flac
*.aac

# -----------------------------------------------------------------------------
# BACKUP FILES
# -----------------------------------------------------------------------------
# Backup files
*.bak
*.backup
*.old
*.orig

# -----------------------------------------------------------------------------
# SECURITY AND CERTIFICATES
# -----------------------------------------------------------------------------
# SSL certificates
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# SSH keys
id_rsa
id_rsa.pub
id_ed25519
id_ed25519.pub

# -----------------------------------------------------------------------------
# PACKAGE MANAGERS
# -----------------------------------------------------------------------------
# Composer (PHP)
vendor/
composer.lock

# Ruby
Gemfile.lock
.bundle/

# Go
go.sum

# Rust
target/
Cargo.lock

# -----------------------------------------------------------------------------
# MISC
# -----------------------------------------------------------------------------
# MacOS
.AppleDouble
.LSOverride

# Windows
*.exe
*.msi
*.msm
*.msp

# Linux
*.AppImage
*.deb
*.rpm

# Archives
*.zip
*.tar.gz
*.rar
*.7z
