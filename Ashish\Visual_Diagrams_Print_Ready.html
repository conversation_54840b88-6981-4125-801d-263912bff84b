<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> Mel<PERSON> 2028 - Visual Diagrams</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 100%;
            margin: 0;
            padding: 20px;
            background: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #FF6B35;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #FF6B35;
            font-size: 28px;
            margin: 0;
            font-weight: bold;
        }
        
        .header h2 {
            color: #666;
            font-size: 18px;
            margin: 10px 0 0 0;
            font-weight: normal;
        }
        
        .diagram-section {
            margin: 40px 0;
            page-break-inside: avoid;
        }
        
        .diagram-title {
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .diagram-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
            text-align: center;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .mermaid {
            width: 100%;
            height: auto;
        }
        
        .description {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #FF6B35;
            border-radius: 4px;
        }
        
        .description h3 {
            color: #FF6B35;
            margin-top: 0;
        }
        
        .key-points {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .key-point {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .key-point h4 {
            color: #FF6B35;
            margin-top: 0;
            font-size: 16px;
        }
        
        .key-point ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .key-point li {
            margin: 5px 0;
            font-size: 14px;
        }
        
        .footer {
            margin-top: 50px;
            text-align: center;
            padding-top: 20px;
            border-top: 2px solid #eee;
            color: #666;
            font-size: 14px;
        }
        
        @media print {
            body {
                padding: 0;
            }
            
            .diagram-section {
                page-break-before: always;
            }
            
            .diagram-section:first-of-type {
                page-break-before: auto;
            }
            
            .diagram-container {
                min-height: 500px;
            }
        }
        
        /* Mermaid diagram styling */
        .mermaid .node rect {
            fill: #f9f9f9;
            stroke: #333;
            stroke-width: 2px;
        }
        
        .mermaid .node .label {
            color: #333;
            font-family: 'Segoe UI', sans-serif;
        }
        
        .mermaid .edgePath .path {
            stroke: #666;
            stroke-width: 2px;
        }
        
        .mermaid .cluster rect {
            fill: #fff;
            stroke: #FF6B35;
            stroke-width: 2px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🕉️ Simhastha Kumbh Mela 2028</h1>
        <h2>Smart Navigation & Safety Platform - Visual Diagrams</h2>
        <p><strong>College Submission Documentation</strong></p>
        <p>Prepared by: Vatsal Patel and Krushil Patel</p>
    </div>

    <!-- Class Diagram Section -->
    <div class="diagram-section">
        <div class="diagram-title">
            📊 Class Diagram - System Architecture
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
classDiagram
    %% Core User Management Classes
    class User {
        +UUID userID
        +String name
        +String phoneNumber
        +String email
        +UserRole role
        +String languagePreference
        +login()
        +updateLocation()
        +createReport()
        +logout()
    }

    class Authentication {
        +UUID sessionID
        +UUID userID
        +String token
        +DateTime expiresAt
        +login()
        +logout()
        +validateToken()
    }

    class Location {
        +UUID locationID
        +Double latitude
        +Double longitude
        +String name
        +String description
        +calculateDistance()
        +getNearbyFacilities()
    }

    class Emergency {
        +UUID reportID
        +UUID userID
        +Location location
        +EmergencyType type
        +Priority priority
        +String description
        +create()
        +update()
        +resolve()
    }

    class Facility {
        +UUID facilityID
        +FacilityType type
        +String name
        +Location location
        +Double rating
        +String openHours
        +updateRating()
        +checkAvailability()
    }

    class Crowd {
        +UUID densityID
        +Location location
        +Integer peopleCount
        +DensityLevel densityLevel
        +updateDensity()
        +generateAlert()
    }

    class MissingPerson {
        +UUID missingID
        +String name
        +Integer age
        +String description
        +Location lastSeenLocation
        +create()
        +generateAlert()
    }

    class Shuttle {
        +UUID shuttleID
        +String routeName
        +Location currentLocation
        +Integer capacity
        +Integer occupancy
        +updateLocation()
        +getETA()
    }

    class Notification {
        +UUID notificationID
        +User user
        +String title
        +String message
        +DateTime timestamp
        +send()
        +markRead()
    }

    %% Relationships
    User ||--|| Authentication : has
    User ||--o{ Emergency : creates
    User ||--o{ MissingPerson : reports
    User ||--o{ Notification : receives
    Emergency ||--|| Location : occurs_at
    Facility ||--|| Location : located_at
    Location ||--o| Crowd : has
    MissingPerson ||--|| Location : last_seen_at
    Shuttle ||--|| Location : current_position
            </div>
        </div>
        
        <div class="description">
            <h3>Class Diagram Analysis</h3>
            <p>This class diagram represents the core object-oriented structure of the Simhastha Kumbh Mela 2028 platform. It shows the main entities, their attributes, methods, and relationships.</p>
            
            <div class="key-points">
                <div class="key-point">
                    <h4>Core Entities</h4>
                    <ul>
                        <li><strong>User:</strong> Central entity representing all system users</li>
                        <li><strong>Location:</strong> Geographic coordinates and place information</li>
                        <li><strong>Emergency:</strong> Emergency reports and incident management</li>
                        <li><strong>Facility:</strong> Services and amenities available to users</li>
                    </ul>
                </div>
                
                <div class="key-point">
                    <h4>Key Relationships</h4>
                    <ul>
                        <li>One-to-Many: User creates multiple Emergency reports</li>
                        <li>One-to-One: User has one Authentication session</li>
                        <li>Location-based: All entities are geographically linked</li>
                        <li>Real-time: Crowd and Shuttle data updates continuously</li>
                    </ul>
                </div>
                
                <div class="key-point">
                    <h4>Design Principles</h4>
                    <ul>
                        <li>Single Responsibility: Each class has a clear purpose</li>
                        <li>Open/Closed: Extensible for new features</li>
                        <li>Dependency Inversion: Abstractions over concretions</li>
                        <li>Composition over Inheritance: Flexible relationships</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Use Case Diagram Section -->
    <div class="diagram-section">
        <div class="diagram-title">
            🎯 Use Case Diagram - System Functionality
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph TB
    %% Actors
    subgraph "Primary Actors"
        P[👤 Pilgrim]
        V[👥 Volunteer]
        PO[👮 Police Officer]
        M[👨‍⚕️ Medical Personnel]
    end

    subgraph "System"
        UC1[Find Nearby Facilities]
        UC2[Navigate to Destination]
        UC3[Report Emergency]
        UC4[Report Missing Person]
        UC5[View Crowd Information]
        UC6[Track Shuttle Services]
        UC7[Receive Notifications]
        UC8[Use AI Assistant]
        UC9[Respond to Emergency]
        UC10[Monitor Security Zones]
        UC11[Manage Medical Facilities]
        UC12[Coordinate Search Operations]
        UC13[Assist Pilgrims]
        UC14[Generate Reports]
        UC15[Register and Login]
    end

    %% Pilgrim Use Cases
    P --> UC1
    P --> UC2
    P --> UC3
    P --> UC4
    P --> UC5
    P --> UC6
    P --> UC7
    P --> UC8
    P --> UC15

    %% Volunteer Use Cases
    V --> UC13
    V --> UC12
    V --> UC7
    V --> UC15

    %% Police Officer Use Cases
    PO --> UC9
    PO --> UC10
    PO --> UC12
    PO --> UC14
    PO --> UC15

    %% Medical Personnel Use Cases
    M --> UC9
    M --> UC11
    M --> UC14
    M --> UC15

    %% Include relationships
    UC3 -.->|includes| UC15
    UC4 -.->|includes| UC15
    UC9 -.->|includes| UC7
            </div>
        </div>
        
        <div class="description">
            <h3>Use Case Diagram Analysis</h3>
            <p>This use case diagram illustrates the functional requirements of the system from the perspective of different user types (actors) and their interactions with the system.</p>
            
            <div class="key-points">
                <div class="key-point">
                    <h4>Primary Actors</h4>
                    <ul>
                        <li><strong>Pilgrim:</strong> Main users seeking navigation and safety services</li>
                        <li><strong>Volunteer:</strong> Helpers assisting pilgrims and coordinating activities</li>
                        <li><strong>Police Officer:</strong> Security personnel managing safety and order</li>
                        <li><strong>Medical Personnel:</strong> Healthcare providers responding to medical needs</li>
                    </ul>
                </div>
                
                <div class="key-point">
                    <h4>Core Use Cases</h4>
                    <ul>
                        <li><strong>Navigation:</strong> Find facilities, get directions, track shuttles</li>
                        <li><strong>Emergency:</strong> Report incidents, coordinate response</li>
                        <li><strong>Information:</strong> View crowd data, receive notifications</li>
                        <li><strong>Communication:</strong> AI assistance, alerts, coordination</li>
                    </ul>
                </div>
                
                <div class="key-point">
                    <h4>System Benefits</h4>
                    <ul>
                        <li>Multi-role support for different user types</li>
                        <li>Real-time information and communication</li>
                        <li>Emergency response coordination</li>
                        <li>Comprehensive navigation and safety features</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <p><strong>Simhastha Kumbh Mela 2028 - Smart Navigation & Safety Platform</strong></p>
        <p>Visual Diagrams Documentation | Generated: September 28, 2025</p>
        <p>Developed by: Vatsal Patel and Krushil Patel</p>
        <p><em>"Where Faith Meets Technology"</em></p>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#FF6B35',
                primaryTextColor: '#333',
                primaryBorderColor: '#FF6B35',
                lineColor: '#666',
                secondaryColor: '#F7931E',
                tertiaryColor: '#fff'
            }
        });
    </script>
</body>
</html>
