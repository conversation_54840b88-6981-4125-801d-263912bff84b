
SUPABASE_URL=https://your-project-id.supabase.co


SUPABASE_ANON_KEY=your_supabase_anon_key_here

SUPABASE_SERVICE_KEY=your_supabase_service_role_key_here


SUPABASE_KEY=your_supabase_anon_key_here

DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres


API_BASE_URL=http://localhost:8000

# API version and environment
API_VERSION=v1
ENVIRONMENT=development


JWT_SECRET=your_jwt_secret_key_here

# JWT Token expiration time (in seconds)
JWT_EXPIRATION=3600

# CORS allowed origins (comma-separated list)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,https://yourdomain.com


TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# AWS S3 for file storage (if using file upload features)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# Google Maps API (if using Google Maps instead of OpenStreetMap)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key


APP_NAME=Simhastha Kumbh Mela 2028
APP_VERSION=1.0.0

# Debug mode (set to false in production)
DEBUG=true

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Maximum file upload size (in bytes)
MAX_UPLOAD_SIZE=********

REDIS_URL=redis://localhost:6379/0

# Redis password (if required)
REDIS_PASSWORD=your_redis_password


SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_USE_TLS=true

SENTRY_DSN=your_sentry_dsn_here

# Google Analytics tracking ID
GA_TRACKING_ID=UA-XXXXXXXX-X


DEV_HOST=localhost
DEV_PORT=8000
DEV_RELOAD=true

TEST_DATABASE_URL=postgresql://postgres:test_password@localhost:5432/test_db


